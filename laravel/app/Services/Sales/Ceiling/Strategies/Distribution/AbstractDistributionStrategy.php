<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\{
    TransactionManagerInterface,
    SettingsProviderInterface,
    SaleProcessorInterface,
    ExcessDistributorInterface,
    LimitCalculatorInterface
};
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\{
    SaleDetailFactory,
    SaleCreator
};
use App\Services\SalesService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

abstract class AbstractDistributionStrategy implements DistributionStrategy, SaleProcessorInterface
{
    protected TransactionManagerInterface $transactionManager;
    protected SettingsProviderInterface $settingsProvider;
    protected LimitCalculatorInterface $limitCalculator;
    protected SaleDetailFactory $saleDetailFactory;
    protected SaleCreator $saleCreator;
    protected SalesService $salesService;

    public function __construct(
        TransactionManagerInterface $transactionManager,
        SettingsProviderInterface $settingsProvider,
        LimitCalculatorInterface $limitCalculator,
        SaleDetailFactory $saleDetailFactory,
        SaleCreator $saleCreator,
        SalesService $salesService
    ) {
        $this->transactionManager = $transactionManager;
        $this->settingsProvider = $settingsProvider;
        $this->limitCalculator = $limitCalculator;
        $this->saleDetailFactory = $saleDetailFactory;
        $this->saleCreator = $saleCreator;
        $this->salesService = $salesService;
    }

    /**
     * Template method that defines the algorithm structure
     *
     * @param Collection $ceilingSales
     * @return bool
     */
    public function recalculateAndDistributeDifferences(Collection $ceilingSales): bool
    {
        try {
            return $this->transactionManager->executeInTransaction(function () use ($ceilingSales) {
                $salesContributionBaseOn = $this->settingsProvider->getSalesContributionSettings();

                foreach ($ceilingSales as $ceilingSale) {
                    if (!$this->processCeilingSale($ceilingSale, $salesContributionBaseOn)) {
                        Log::warning('Failed to process ceiling sale', ['ceiling_sale_id' => $ceilingSale->id ?? 'unknown']);
                        continue;
                    }
                }

                return true;
            });
        } catch (\Throwable $e) {
            Log::error('Distribution strategy failed', [
                'strategy' => static::class,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Process a single ceiling sale - Template method
     *
     * @param mixed $ceilingSale
     * @param array $salesContributionBaseOn
     * @return bool
     */
    public function processCeilingSale($ceilingSale, array $salesContributionBaseOn): bool
    {
        // Step 1: Validate ceiling sale
        if (!$this->validateCeilingSale($ceilingSale)) {
            return false;
        }

        // Step 2: Get original sale (strategy-specific)
        $originalSale = $this->getOriginalSale($ceilingSale);
        if (!$originalSale) {
            return false;
        }

        // Step 3: Create limited sale
        if (!$this->createLimitedSaleDistribution($ceilingSale, $originalSale)) {
            return false;
        }

        // Step 4: Update original sales ceiling status
        if (!$this->updateOriginalSalesCeiling($ceilingSale)) {
            return false;
        }

        // Step 5: Create and distribute excess sale (strategy-specific)
        return $this->createAndDistributeExcessSale($ceilingSale, $originalSale, $salesContributionBaseOn);
    }

    /**
     * Update original sales ceiling status
     *
     * @param mixed $ceilingSale
     * @return bool
     */
    public function updateOriginalSalesCeiling($ceilingSale): bool
    {
        return $this->saleCreator->updateOriginalSalesCeiling($ceilingSale);
    }

    /**
     * Validate ceiling sale data
     *
     * @param mixed $ceilingSale
     * @return bool
     */
    protected function validateCeilingSale($ceilingSale): bool
    {
        return isset($ceilingSale->sale_ids) &&
               isset($ceilingSale->number_of_units) &&
               $this->limitCalculator->exceedsLimit($ceilingSale);
    }

    /**
     * Create limited sale distribution
     *
     * @param mixed $ceilingSale
     * @param Sale $originalSale
     * @return bool
     */
    protected function createLimitedSaleDistribution($ceilingSale, Sale $originalSale): bool
    {
        if ($originalSale->quantity == 0) {
            return false;
        }

        $limitQuantity = $this->limitCalculator->calculateLimit($ceilingSale);
        $limitedSale = $this->saleCreator->createLimitedSale($ceilingSale, $originalSale, $limitQuantity);

        return $this->saleDetailFactory->createLimitedSaleDetails($originalSale, $limitedSale, $ceilingSale);
    }

    // Abstract methods that must be implemented by concrete strategies

    /**
     * Get the original sale for processing
     *
     * @param mixed $ceilingSale
     * @return Sale|null
     */
    protected function getOriginalSale($ceilingSale): ?Sale
    {
        $originalSaleId = explode(',', $ceilingSale->sale_ids)[0];
        return Sale::whereId($originalSaleId)->with('details')->first();
    }

    /**
     * Create and distribute excess sale according to strategy-specific logic
     *
     * @param mixed $ceilingSale
     * @param Sale $originalSale
     * @param array $salesContributionBaseOn
     * @return bool
     */
    abstract protected function createAndDistributeExcessSale($ceilingSale, Sale $originalSale, array $salesContributionBaseOn): bool;

    /**
     * Get the DistributionType for this strategy
     *
     * @return DistributionType
     */
    abstract protected function getDistributionType(): DistributionType;
}
