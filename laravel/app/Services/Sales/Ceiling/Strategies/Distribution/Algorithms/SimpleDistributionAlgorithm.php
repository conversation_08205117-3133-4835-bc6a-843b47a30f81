<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms;

use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\Enums\SaleDistribution;
use App\Services\SalesService;

/**
 * Simple distribution algorithm that distributes 100% of excess sales
 * using normal distribution ratios (used by PrivatePharmacyStrategy)
 */
class SimpleDistributionAlgorithm implements ExcessDistributorInterface
{
    private SalesService $salesService;
    private SaleDetailFactory $saleDetailFactory;

    public function __construct(SalesService $salesService, SaleDetailFactory $saleDetailFactory)
    {
        $this->salesService = $salesService;
        $this->saleDetailFactory = $saleDetailFactory;
    }

    /**
     * Distribute excess sale using simple 100% distribution
     *
     * @param Sale $sale
     * @param array $salesContributionBaseOn
     * @param mixed $originalSale
     * @param DistributionType|null $distributionType
     * @return bool
     */
    public function distributeExcessSale(Sale $sale, array $salesContributionBaseOn,?Sale $originalSale = null, ?DistributionType $distributionType = null): bool
    {
        // Create SalesService with the appropriate DistributionType
        $salesService = $distributionType
            ? SalesService::make(SaleDistribution::NORMAL, $distributionType)
            : $this->salesService;

        $divisionsBricks = $salesService
            ->getRatiosForDistribution(
                $sale->date,
                $sale->product_id,
                $salesContributionBaseOn
            );

        $details = $this->saleDetailFactory->createDetailsFromRatios($sale, $divisionsBricks->toArray());

        return $this->saleDetailFactory->insertDetails($details);
    }

    /**
     * Calculate excess quantity for distribution
     * Updated to handle null limits from LEFT JOIN (STORES distribution type)
     *
     * @param mixed $ceilingSale
     * @return float
     */
    public function calculateExcessQuantity($ceilingSale): float
    {
        $limit = 0;
        if ($ceilingSale->number_of_units > 0) {
            $limit = $ceilingSale->limit ?? 0;
        }

        if ($ceilingSale->number_of_units < 0) {
            $limit = $ceilingSale->negative_limit ?? 0;
        }

        return $ceilingSale->number_of_units - $limit;
    }
}
