<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms;

use App\LineDivision;
use App\Sale;
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\ExcessDistributorInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\DistributionType;
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\SaleDetailFactory;
use App\Services\Enums\SaleDistribution;
use App\Services\SalesService;
use Illuminate\Support\Collection;

/**
 * Split distribution algorithm that distributes excess sales using 90/10 split
 * (used by StoreStrategy)
 */
class SplitDistributionAlgorithm implements ExcessDistributorInterface
{
    private SalesService $salesService;
    private SaleDetailFactory $saleDetailFactory;
    private float $primaryPercentage;
    private float $secondaryPercentage;

    public function __construct(
        SalesService $salesService,
        SaleDetailFactory $saleDetailFactory,
        float $primaryPercentage = 0.9,
        float $secondaryPercentage = 0.1
    ) {
        $this->salesService = $salesService;
        $this->saleDetailFactory = $saleDetailFactory;
        $this->primaryPercentage = $primaryPercentage;
        $this->secondaryPercentage = $secondaryPercentage;
    }

    /**
     * Distribute excess sale using 90/10 split distribution
     *
     * @param Sale $sale
     * @param array $salesContributionBaseOn
     * @param mixed $originalSale
     * @param DistributionType|null $distributionType
     * @return bool
     */
    public function distributeExcessSale(Sale $sale, array $salesContributionBaseOn,?Sale $originalSale = null, ?DistributionType $distributionType = null): bool
    {
        // Get primary distribution ratios (90%)
        $primaryRatios = $this->getPrimaryDistributionRatios($sale, $salesContributionBaseOn, $distributionType);
        $primaryDetails = $this->saleDetailFactory->createDetailsFromRatios(
            $sale,
            $primaryRatios->toArray(),
            $this->primaryPercentage,
            $this->primaryPercentage,
            $this->primaryPercentage,
            $this->primaryPercentage
        );

        // Get secondary distribution ratios (10%)
        $secondaryRatios = $this->getSecondaryDistributionRatios($sale, $originalSale, $salesContributionBaseOn, $distributionType);
        $secondaryDetails = $this->saleDetailFactory->createDetailsFromRatios(
            $sale,
            $secondaryRatios->toArray(),
            $this->secondaryPercentage,
            $this->secondaryPercentage,
            $this->secondaryPercentage,
            $this->secondaryPercentage
        );

        $allDetails = array_merge($primaryDetails, $secondaryDetails);

        return $this->saleDetailFactory->insertDetails($allDetails);
    }

    /**
     * Calculate excess quantity for distribution
     * Updated to handle null limits from LEFT JOIN (STORES distribution type)
     *
     * @param mixed $ceilingSale
     * @return float
     */
    public function calculateExcessQuantity($ceilingSale): float
    {
        // Calculate the actual excess above the limit
        $limit = 0;
        if ($ceilingSale->number_of_units > 0) {
            $limit = $ceilingSale->limit ?? 0;
        }

        if ($ceilingSale->number_of_units < 0) {
            $limit = $ceilingSale->negative_limit ?? 0;
        }

        return $ceilingSale->number_of_units - $limit;
    }

    /**
     * Get primary distribution ratios (90% portion)
     *
     * @param Sale $sale
     * @param array $salesContributionBaseOn
     * @param DistributionType|null $distributionType
     * @return Collection
     */
    private function getPrimaryDistributionRatios(Sale $sale, array $salesContributionBaseOn, ?DistributionType $distributionType = null): Collection
    {
        // Create SalesService with the appropriate DistributionType
        $salesService = $distributionType
            ? SalesService::make(SaleDistribution::NORMAL, $distributionType)
            : $this->salesService;

        return $salesService
            ->getRatiosForDistribution(
                $sale->date,
                $sale->product_id,
                $salesContributionBaseOn
            );
    }

    /**
     * Get secondary distribution ratios (10% portion)
     *
     * @param Sale $sale
     * @param mixed $originalSale
     * @param array $salesContributionBaseOn
     * @param DistributionType|null $distributionType
     * @return Collection
     */
    private function getSecondaryDistributionRatios(Sale $sale,?Sale $originalSale, array $salesContributionBaseOn, ?DistributionType $distributionType = null): Collection
    {
        if (!$originalSale) {
            // Fallback to primary distribution if no original sale
            return $this->getPrimaryDistributionRatios($sale, $salesContributionBaseOn, $distributionType);
        }

        $divisionIds = $this->getDeepestDescendantIds($originalSale);

        // Create SalesService with the appropriate DistributionType
        $salesService = $distributionType
            ? SalesService::make(SaleDistribution::NORMAL, $distributionType)
            : $this->salesService;

        return $salesService
            ->getRatiosForDistribution(
                $sale->date,
                $sale->product_id,
                $salesContributionBaseOn,
                $divisionIds
            );
    }

    /**
     * Get deepest descendant division IDs from original sale
     *
     * @param mixed $originalSale
     * @return array
     */
    private function getDeepestDescendantIds(Sale $originalSale): array
    {
        // Check if details exist and is not null
        if (!$originalSale->details) {
            return [];
        }

        $divisionIds = $originalSale->details->pluck('div_id')->toArray();

        if (empty($divisionIds)) {
            return [];
        }

        return LineDivision::whereIn('id', $divisionIds)
            ->get()
            ->map(function (LineDivision $lineDivision) {
                return $lineDivision->getDeepestDescendantsAtLevel(2)->pluck('id')->toArray();
            })
            ->flatten()
            ->unique()
            ->values()
            ->toArray();
    }
}
